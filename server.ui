<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Server</class>
 <widget class="QMainWindow" name="Server">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1100</width>
    <height>600</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1100</width>
    <height>600</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>1100</width>
    <height>600</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>server</string>
  </property>
  <property name="windowIcon">
   <iconset theme="QIcon::ThemeIcon::CallStart"/>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QWidget" name="layoutWidget">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>10</y>
      <width>1061</width>
      <height>561</height>
     </rect>
    </property>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QComboBox" name="whichtable">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Fixed" vsizetype="Maximum">
           <horstretch>50</horstretch>
           <verstretch>50</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>150</width>
           <height>32</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">font: 10pt &quot;Microsoft YaHei UI&quot;;

</string>
         </property>
         <item>
          <property name="text">
           <string>Users</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>Messages</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>Friends</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>FriendRequests </string>
          </property>
         </item>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="listen">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Maximum" vsizetype="Minimum">
           <horstretch>0</horstretch>
           <verstretch>9</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>80</width>
           <height>27</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QPushButton{
font: 10pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3); 
}
  QPushButton:pressed {
font: 10pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3); 
background-color: rgb(255, 251, 255); 
   }</string>
         </property>
         <property name="text">
          <string>开启服务器</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
         <property name="default">
          <bool>false</bool>
         </property>
         <property name="flat">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item alignment="Qt::AlignmentFlag::AlignLeft">
        <widget class="QPushButton" name="pushButton">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
           <horstretch>0</horstretch>
           <verstretch>10</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>80</width>
           <height>27</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QPushButton{
font: 10pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3); 
}
  QPushButton:pressed {
font: 10pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3); 
background-color: rgb(255, 251, 255); 
   }</string>
         </property>
         <property name="text">
          <string>刷新表格</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QTableView" name="table">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>50</horstretch>
         <verstretch>50</verstretch>
        </sizepolicy>
       </property>
       <property name="styleSheet">
        <string notr="true">font: 10pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3); 
</string>
       </property>
       <property name="alternatingRowColors">
        <bool>false</bool>
       </property>
       <attribute name="horizontalHeaderCascadingSectionResizes">
        <bool>false</bool>
       </attribute>
       <attribute name="verticalHeaderCascadingSectionResizes">
        <bool>false</bool>
       </attribute>
       <attribute name="verticalHeaderShowSortIndicator" stdset="0">
        <bool>false</bool>
       </attribute>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QLineEdit" name="sql">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Minimum" vsizetype="Maximum">
           <horstretch>50</horstretch>
           <verstretch>50</verstretch>
          </sizepolicy>
         </property>
         <property name="styleSheet">
          <string notr="true">font: 12pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3); 
</string>
         </property>
         <property name="text">
          <string>请输入SQL语句</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="sqlsubmit">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Minimum" vsizetype="Maximum">
           <horstretch>5</horstretch>
           <verstretch>50</verstretch>
          </sizepolicy>
         </property>
         <property name="styleSheet">
          <string notr="true">QPushButton{
font: 12pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3); 
}
  QPushButton:pressed {
font: 12pt &quot;Microsoft YaHei UI&quot;;
border: 1px solid rgba(0, 0, 0, 0.3); 
background-color: rgb(255, 251, 255); 
   }</string>
         </property>
         <property name="text">
          <string>提交</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1100</width>
     <height>18</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
